🚀 相机标定系统启动中...

📋 加载配置文件...
✅ 配置加载成功
📷 输入图像: ../data/input/0807/20250807-195926.bmp (尺寸: 800x600)
📁 输出目录: output/

🔧 初始化标定管理器...
📁 调试目录: ./debug/
🐛 调试模式已启用
[CalibrationManager INFO] 从配置文件读取网格维度:
[CalibrationManager INFO]   配置中的 grid_rows: 7
[CalibrationManager INFO]   配置中的 grid_cols: 14
[CalibrationManager INFO]   设置 ROWS_: 7
[CalibrationManager INFO]   设置 COLS_: 14
[CalibrationManager INFO] 世界坐标轴加载完成:
[CalibrationManager INFO]   h_axis 长度: 7
[CalibrationManager INFO]   w_axis 长度: 14
[CalibrationManager INFO]   DIS_TO_CAMERA: 1.000000
[CalibrationManager INFO]   DIS_TO_CAMERA2CENTER: 0.000000
[CalibrationManager INFO] 网格数据结构初始化完成: 7x14 = 98 个网格点
[ForwardCalibrator INFO] 前向标定器初始化成功
[ImageEnhancer INFO] 图像增强器初始化成功
[CoordinateMapper INFO] 坐标映射器初始化成功
✅ 标定管理器初始化成功
-------------------------------------------------------

🎯 开始执行标定流程...
[ForwardCalibrator INFO] 开始前向标定: ../data/input/0807/20250807-195926.bmp
[ForwardCalibrator INFO] 图像已保存: ./debug/1distortion.bmp
[ForwardCalibrator DEBUG] 调试图像已保存: ./debug/1distortion.bmp
[ForwardCalibrator INFO] 图像加载成功: 800x600, 通道数: 3
[ForwardCalibrator INFO] 开始去畸变
[ForwardCalibrator INFO] 图像已保存: ./debug/2undistortion.bmp
[ForwardCalibrator DEBUG] 调试图像已保存: ./debug/2undistortion.bmp
[ForwardCalibrator INFO] 去畸变处理完成
[ForwardCalibrator DEBUG] 棋盘格检测尺寸: 11x8
[ForwardCalibrator INFO] 图像已保存: ./debug/3Chessboard.bmp
[ForwardCalibrator DEBUG] 调试图像已保存: ./debug/3Chessboard.bmp
[ForwardCalibrator INFO] 棋盘格检测成功，角点数量: 88
[ForwardCalibrator INFO] 开始计算透视变换矩阵...
width: 136
height: 95
[ForwardCalibrator DEBUG] 棋盘格边界: (347,211) 到 (483,306)
[ForwardCalibrator DEBUG] 棋盘格尺寸: 11x8
[ForwardCalibrator INFO] 透视变换矩阵计算完成，重投影误差: 0.072445
[ForwardCalibrator INFO] 图像已保存: ./debug/4warp.bmp
[ForwardCalibrator DEBUG] 调试图像已保存: ./debug/4warp.bmp
[ForwardCalibrator INFO] 透视变换完成
pixel_value1: [206, 211, 210]
599
300
0
pixel_value1: [204, 209, 207]
599
301
0
pixel_value1: [204, 209, 207]
599
302
0
pixel_value1: [206, 211, 209]
599
303
0
pixel_value1: [210, 214, 212]
599
304
0
pixel_value1: [213, 218, 216]
599
305
0
pixel_value1: [213, 217, 215]
599
306
0
pixel_value1: [210, 215, 213]
599
307
0
pixel_value1: [208, 214, 212]
599
308
0
pixel_value1: [208, 214, 212]
599
309
0
pixel_value1: [209, 216, 213]
599
310
0
pixel_value1: [210, 217, 214]
599
311
0
pixel_value1: [209, 217, 213]
599
312
0
pixel_value1: [209, 216, 213]
599
313
0
pixel_value1: [209, 215, 213]
599
314
0
pixel_value1: [210, 215, 213]
599
315
0
pixel_value1: [210, 215, 213]
599
316
0
pixel_value1: [209, 214, 212]
599
317
0
pixel_value1: [210, 215, 213]
599
318
0
pixel_value1: [213, 216, 214]
599
319
0
pixel_value1: [213, 216, 215]
599
320
0
pixel_value1: [211, 214, 212]
599
321
0
pixel_value1: [211, 215, 212]
599
322
0
pixel_value1: [212, 217, 213]
599
323
0
pixel_value1: [214, 220, 214]
599
324
0
pixel_value1: [214, 219, 214]
599
325
0
pixel_value1: [214, 219, 214]
599
326
0
pixel_value1: [213, 218, 213]
599
327
0
pixel_value1: [214, 218, 213]
599
328
0
pixel_value1: [214, 219, 214]
599
329
0
pixel_value1: [212, 218, 213]
599
330
0
pixel_value1: [210, 216, 211]
599
331
0
pixel_value1: [210, 216, 211]
599
332
0
pixel_value1: [212, 218, 213]
599
333
0
pixel_value1: [213, 219, 215]
599
334
0
pixel_value1: [212, 218, 214]
599
335
0
pixel_value1: [211, 217, 214]
599
336
0
pixel_value1: [210, 216, 212]
599
337
0
pixel_value1: [210, 216, 212]
599
338
0
pixel_value1: [211, 216, 213]
599
339
0
pixel_value1: [212, 217, 214]
599
340
0
pixel_value1: [212, 217, 214]
599
341
0
pixel_value1: [212, 218, 215]
599
342
0
pixel_value1: [212, 218, 215]
599
343
0
pixel_value1: [212, 218, 215]
599
344
0
pixel_value1: [212, 218, 215]
599
345
0
pixel_value1: [213, 219, 216]
599
346
0
pixel_value1: [215, 221, 218]
599
347
0
pixel_value1: [215, 221, 219]
599
348
0
pixel_value1: [213, 219, 216]
599
349
0
pixel_value1: [213, 218, 216]
599
350
0
pixel_value1: [214, 219, 217]
599
351
0
pixel_value1: [213, 218, 216]
599
352
0
pixel_value1: [211, 216, 214]
599
353
0
pixel_value1: [210, 215, 213]
599
354
0
pixel_value1: [213, 219, 215]
599
355
0
pixel_value1: [215, 221, 216]
599
356
0
pixel_value1: [215, 221, 216]
599
357
0
pixel_value1: [214, 221, 215]
599
358
0
pixel_value1: [214, 220, 215]
599
359
0
pixel_value1: [214, 220, 215]
599
360
0
pixel_value1: [213, 220, 215]
599
361
0
pixel_value1: [214, 220, 215]
599
362
0
pixel_value1: [215, 220, 215]
599
363
0
pixel_value1: [216, 220, 215]
599
364
0
pixel_value1: [216, 220, 215]
599
365
0
pixel_value1: [215, 219, 215]
599
366
0
pixel_value1: [215, 219, 214]
599
367
0
pixel_value1: [214, 218, 214]
599
368
0
pixel_value1: [214, 217, 213]
599
369
0
pixel_value1: [213, 217, 213]
599
370
0
pixel_value1: [214, 218, 213]
599
371
0
pixel_value1: [215, 218, 213]
599
372
0
pixel_value1: [216, 220, 215]
599
373
0
pixel_value1: [217, 221, 216]
599
374
0
pixel_value1: [218, 222, 217]
599
375
0
pixel_value1: [219, 223, 218]
599
376
0
pixel_value1: [219, 222, 217]
599
377
0
pixel_value1: [217, 221, 216]
599
378
0
pixel_value1: [216, 221, 216]
599
379
0
pixel_value1: [215, 221, 216]
599
380
0
pixel_value1: [214, 220, 215]
599
381
0
pixel_value1: [212, 218, 213]
599
382
0
pixel_value1: [212, 218, 213]
599
383
0
pixel_value1: [213, 219, 214]
599
384
0
pixel_value1: [214, 220, 215]
599
385
0
pixel_value1: [214, 220, 215]
599
386
0
pixel_value1: [215, 221, 216]
599
387
0
pixel_value1: [215, 221, 216]
599
388
0
pixel_value1: [214, 220, 215]
599
389
0
pixel_value1: [214, 220, 215]
599
390
0
pixel_value1: [215, 221, 216]
599
391
0
pixel_value1: [216, 222, 217]
599
392
0
pixel_value1: [215, 221, 216]
599
393
0
pixel_value1: [215, 221, 216]
599
394
0
pixel_value1: [215, 221, 217]
599
395
0
pixel_value1: [215, 220, 219]
599
396
0
pixel_value1: [213, 218, 217]
599
397
0
pixel_value1: [211, 216, 214]
599
398
0
pixel_value1: [211, 216, 214]
599
399
0
pixel_value1: [214, 219, 217]
599
400
0
pixel_value1: [216, 221, 219]
599
401
0
pixel_value1: [217, 222, 220]
599
402
0
pixel_value1: [215, 220, 218]
599
403
0
pixel_value1: [214, 219, 217]
599
404
0
pixel_value1: [214, 219, 217]
599
405
0
pixel_value1: [215, 220, 218]
599
406
0
pixel_value1: [216, 221, 219]
599
407
0
pixel_value1: [216, 222, 221]
599
408
0
pixel_value1: [215, 221, 220]
599
409
0
pixel_value1: [214, 220, 219]
599
410
0
pixel_value1: [212, 218, 216]
599
411
0
pixel_value1: [209, 215, 214]
599
412
0
pixel_value1: [210, 216, 215]
599
413
0
pixel_value1: [212, 218, 216]
599
414
0
pixel_value1: [211, 217, 216]
599
415
0
pixel_value1: [209, 216, 214]
599
416
0
pixel_value1: [211, 218, 216]
599
417
0
pixel_value1: [216, 223, 220]
599
418
0
pixel_value1: [218, 225, 221]
599
419
0
pixel_value1: [219, 224, 220]
599
420
0
pixel_value1: [218, 223, 219]
599
421
0
pixel_value1: [217, 223, 217]
599
422
0
pixel_value1: [216, 222, 218]
599
423
0
pixel_value1: [215, 222, 219]
599
424
0
pixel_value1: [213, 220, 217]
599
425
0
pixel_value1: [212, 219, 216]
599
426
0
pixel_value1: [214, 221, 220]
599
427
0
pixel_value1: [218, 226, 225]
599
428
0
pixel_value1: [218, 226, 225]
599
429
0
pixel_value1: [215, 223, 222]
599
430
0
pixel_value1: [213, 221, 220]
599
431
0
pixel_value1: [211, 219, 219]
599
432
0
pixel_value1: [214, 222, 222]
599
433
0
pixel_value1: [220, 228, 228]
599
434
0
pixel_value1: [222, 229, 229]
599
435
0
pixel_value1: [220, 227, 226]
599
436
0
pixel_value1: [217, 223, 222]
599
437
0
pixel_value1: [214, 220, 219]
599
438
0
pixel_value1: [211, 217, 216]
599
439
0
pixel_value1: [210, 217, 214]
599
440
0
pixel_value1: [219, 226, 223]
599
441
0
pixel_value1: [230, 237, 234]
599
442
0
pixel_value1: [225, 232, 229]
599
443
0
pixel_value1: [210, 217, 214]
599
444
0
pixel_value1: [207, 214, 211]
599
445
0
pixel_value1: [213, 220, 217]
599
446
0
pixel_value1: [216, 223, 221]
599
447
0
pixel_value1: [215, 221, 220]
599
448
0
pixel_value1: [217, 222, 222]
599
449
0
pixel_value1: [220, 225, 225]
599
450
0
pixel_value1: [220, 226, 225]
599
451
0
pixel_value1: [217, 223, 222]
599
452
0
pixel_value1: [216, 224, 220]
599
453
0
pixel_value1: [217, 224, 222]
599
454
0
pixel_value1: [215, 222, 219]
599
455
0
pixel_value1: [210, 218, 215]
599
456
0
pixel_value1: [209, 216, 213]
599
457
0
pixel_value1: [210, 217, 215]
599
458
0
pixel_value1: [212, 219, 217]
599
459
0
pixel_value1: [214, 220, 218]
599
460
0
pixel_value1: [212, 219, 216]
599
461
0
pixel_value1: [209, 216, 213]
599
462
0
pixel_value1: [215, 222, 219]
599
463
0
pixel_value1: [224, 231, 228]
599
464
0
pixel_value1: [222, 229, 226]
599
465
0
pixel_value1: [209, 216, 213]
599
466
0
pixel_value1: [205, 212, 209]
599
467
0
pixel_value1: [215, 222, 219]
599
468
0
pixel_value1: [226, 233, 230]
599
469
0
pixel_value1: [229, 236, 233]
599
470
0
pixel_value1: [234, 241, 238]
599
471
0
pixel_value1: [241, 248, 245]
599
472
0
pixel_value1: [245, 251, 248]
599
473
0
pixel_value1: [241, 248, 244]
599
474
0
pixel_value1: [232, 239, 236]
599
475
0
pixel_value1: [222, 229, 226]
599
476
0
pixel_value1: [220, 227, 224]
599
477
0
pixel_value1: [230, 236, 233]
599
478
0
pixel_value1: [234, 240, 237]
599
479
0
pixel_value1: [228, 234, 231]
599
480
0
pixel_value1: [222, 229, 225]
599
481
0
pixel_value1: [222, 229, 226]
599
482
0
pixel_value1: [228, 235, 231]
599
483
0
pixel_value1: [236, 243, 238]
599
484
0
pixel_value1: [238, 245, 240]
599
485
0
pixel_value1: [232, 239, 234]
599
486
0
pixel_value1: [222, 229, 224]
599
487
0
pixel_value1: [217, 224, 219]
599
488
0
pixel_value1: [215, 222, 217]
599
489
0
pixel_value1: [219, 226, 221]
599
490
0
pixel_value1: [224, 231, 226]
599
491
0
pixel_value1: [228, 235, 231]
599
492
0
pixel_value1: [231, 236, 234]
599
493
0
pixel_value1: [230, 235, 233]
599
494
0
pixel_value1: [229, 234, 232]
599
495
0
pixel_value1: [233, 238, 236]
599
496
0
pixel_value1: [238, 243, 241]
599
497
0
pixel_value1: [242, 247, 245]
599
498
0
pixel_value1: [243, 248, 246]
599
499
0
bottom: 0
[ForwardCalibrator INFO] 检测到盲区底部位置: 0
[ForwardCalibrator INFO] 盲区检测完成
[ForwardCalibrator INFO] 开始计算反向透视变换矩阵...
[ForwardCalibrator INFO] 反向透视变换矩阵计算完成
[ForwardCalibrator INFO] 开始计算Mapx和Mapy
[ForwardCalibrator INFO] 标定映射表初始化完成
[ForwardCalibrator INFO] 标定映射生成完成，映射表尺寸: 800x600
[ForwardCalibrator INFO] 图像已保存: ./debug/5beforepost_output.bmp
[ForwardCalibrator DEBUG] 调试图像已保存: ./debug/5beforepost_output.bmp
[ForwardCalibrator DEBUG] 后处理完成，遮盖区域高度: 326
[ForwardCalibrator INFO] 图像已保存: ./debug/6output.bmp
[ForwardCalibrator DEBUG] 调试图像已保存: ./debug/6output.bmp
[ForwardCalibrator INFO] 前向棋盘格标定完成
[ForwardCalibrator INFO] 前向标定完成，结果保存至: output/6output.bmp
[CalibrationManager INFO] 前向标定完成
[CalibrationManager INFO] 开始图像增强处理...
[ImageEnhancer INFO] 使用MSRCR算法增强图像...
[ImageEnhancer INFO] MSRCR图像增强完成
[ImageEnhancer INFO] 黑色区域加深处理 - 阈值: 160, 加深程度: 80
[ImageEnhancer INFO] 图像已保存: ./darkened_enLargePics.bmp
[ImageEnhancer INFO] 地面区域增强完成
[ImageEnhancer INFO] 形态学操作完成
[ImageEnhancer INFO] 图像增强处理完成
[CalibrationManager INFO] 图像已保存: ./debug/7out_enhance.bmp
[CalibrationManager DEBUG] 增强图像已保存: ./debug/7out_enhance.bmp
[CalibrationManager INFO] 开始特征点检测...
[FeatureDetector INFO] 开始特征点检测...
[FeatureDetector INFO] 检测到 60 个原始关键点
[FeatureDetector INFO] 图像已保存: ./debug/8out_enhanceBolb.bmp
[FeatureDetector DEBUG] 增强图像已保存: ./debug/8out_enhanceBolb.bmp
[FeatureDetector INFO] 点集分类完成，左侧: 30 个，右侧: 30 个
[FeatureDetector INFO] 左侧点标记完成，共处理 30 个点
[FeatureDetector INFO] 右侧点标记完成，共处理 30 个点
[FeatureDetector INFO] 特征点标记完成，左侧: 0 个，右侧: 0 个
[FeatureDetector INFO] 图像已保存: ./debug/9output_sorted.bmp
[FeatureDetector DEBUG] 增强图像已保存: ./debug/9output_sorted.bmp
[FeatureDetector INFO] 特征点列数验证通过
[FeatureDetector INFO] 特征点检测完成，有效点数: 60
[CalibrationManager INFO] 开始坐标映射...
[CalibrationManager INFO] 标定结果已保存到: output/output.csv
[CalibrationManager INFO] 生成标定查找表: distance_table
[CalibrationManager INFO] 查找表参数: table_height=281, table_width=801
[CalibrationManager INFO] CSV数据加载完成，共处理 60 条记录
[CalibrationManager INFO] 逐行插值处理完成
[CalibrationManager INFO] >>>Done!
[CalibrationManager INFO] >>>Number of Points Processed: 450162
[CalibrationManager INFO] 查找表生成完成
-------------------------------------------------------

🎉 标定成功完成！

📊 标定统计信息:
   验证状态: 通过
   备注: 标定成功完成

⏱️  总耗时: 250 毫秒
📁 结果已保存到: output/
