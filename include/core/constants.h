/**
 * @file constants.h
 * @brief 系统常量定义
 * <AUTHOR> Calibration Team
 * @date 2024
 */

#ifndef CAMERA_CALIBRATION_CORE_CONSTANTS_H
#define CAMERA_CALIBRATION_CORE_CONSTANTS_H

#include <cstdint>
#include <cstddef>

namespace camera_calibration {
namespace constants {

// ============================================================================
// 像素值常量
// ============================================================================
constexpr uint8_t WHITE_PIXEL_VALUE = 255;
constexpr uint8_t BLACK_PIXEL_VALUE = 0;

// 默认 MSRCR 权重和尺度
constexpr double DEFAULT_WEIGHTS[] = {0.1, 0.1, 0.1};
constexpr double DEFAULT_SIGMAS[] = {30.0, 150.0, 300.0};
constexpr int DEFAULT_MSRCR_SCALES = 3;

// ============================================================================
// 盲区检测参数
// ============================================================================
constexpr int BLIND_AREA_DETECTION_RANGE = 100;  // center ± 100 像素
constexpr int MAX_BLIND_AREA_SIZE = 200;          // 最大盲区高度
constexpr int MIN_BLIND_AREA_SIZE = 5;            // 最小盲区高度

// ============================================================================
// 棋盘格检测参数
// ============================================================================
// 角点检测参数
constexpr double CORNER_SUB_PIX_WINDOW_SIZE = 11.0;
constexpr int CORNER_SUB_PIX_MAX_ITERATIONS = 30;
constexpr double CORNER_SUB_PIX_EPSILON = 0.1;

// ============================================================================
// MSRCR 图像增强参数
// ============================================================================
constexpr double DEFAULT_MSRCR_GAIN = 128.0;
constexpr double DEFAULT_MSRCR_OFFSET = 128.0;
constexpr double DEFAULT_RESTORATION_FACTOR = 6.0;
constexpr double DEFAULT_COLOR_GAIN = 2.0;
constexpr double MAX_SIGMA_VALUE = 300.0;
constexpr double MIN_SIGMA_VALUE = 1.0;



// ============================================================================
// 矩阵和数组维度
// ============================================================================
constexpr int CAMERA_MATRIX_ROWS = 3;
constexpr int CAMERA_MATRIX_COLS = 3;
constexpr int DISTORTION_COEFFS_SIZE = 8;





// 重构中使用的关键常量
constexpr int POINT_DISTANCE_THRESHOLD = 10;
constexpr int COLUMN_WIDTH_THRESHOLD = 500;

// 形态学操作
constexpr int DEFAULT_KERNEL_SIZE = 2;
constexpr int DEFAULT_DILATE_ITERATIONS = 1;
constexpr int DEFAULT_ERODE_ITERATIONS = 3;

// 边缘处理
constexpr int DEFAULT_BORDER_WIDTH = 50;
constexpr int DEFAULT_BORDER_HEIGHT = 20;


// ============================================================================
// 错误值和状态码
// ============================================================================
constexpr double INVALID_COORDINATE = -1.0;

// ============================================================================
// 数值计算常量
// ============================================================================
constexpr double PI = 3.14159265358979323846;


// ============================================================================
// 标准文件名
// ============================================================================
namespace standard_filenames {
    constexpr const char* ORIGINAL_IMAGE = "1distortion.bmp";
    constexpr const char* UNDISTORTED_IMAGE = "2undistortion.bmp";
    constexpr const char* CHESSBOARD_IMAGE = "3Chessboard.bmp";
    constexpr const char* WARPED_IMAGE = "4warp.bmp";
    constexpr const char* FINAL_CALIBRATED_IMAGE = "6output.bmp";
    constexpr const char* beforepost_CALIBRATED_IMAGE = "5beforepost_output.bmp";
    constexpr const char* ENHANCED_IMAGE = "7out_enhance.bmp";
    constexpr const char* BLOB_IMAGE = "8out_enhanceBolb.bmp";
    constexpr const char* FEATURE_IMAGE = "9output_blob.bmp";
    constexpr const char* SORTED_IMAGE = "9output_sorted.bmp";
    
    constexpr const char* OUTPUT_CSV = "output.csv";
    constexpr const char* DISTANCE_TABLE = "distance_table";
    constexpr const char* LOG_FILE = "Log.txt";
    constexpr const char* WARPED_FILE = "warped.txt";
    constexpr const char* LOC_FILE = "loc.txt";

    constexpr const char* MAPX_FILE = "mapx";
    constexpr const char* MAPY_FILE = "mapy";
}

} // namespace constants
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_CORE_CONSTANTS_H
