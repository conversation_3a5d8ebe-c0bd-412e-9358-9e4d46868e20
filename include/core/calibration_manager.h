#ifndef CAMERA_CALIBRATION_CORE_CALIBRATION_MANAGER_H
#define CAMERA_CALIBRATION_CORE_CALIBRATION_MANAGER_H

#include "types.h"
#include "config_manager.h"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>
#include <memory>

namespace camera_calibration {

// 前向声明
namespace calibration {
    class ForwardCalibrator;
}

namespace processing {
    class ImageEnhancer;
    class FeatureDetector;
    class CoordinateMapper;
}

namespace core {

/**
 * @brief 标定管理器类
 * 
 * 这是整个标定系统的核心控制器，负责协调各个模块的工作：
 * - 管理标定流程
 * - 协调各个处理模块
 * - 处理结果输出和保存
 */
class CalibrationManager {
public:
    /**
     * @brief 构造函数
     * @param config_manager 配置管理器引用
     */
    explicit CalibrationManager(ConfigManager& config_manager);
    
    /**
     * @brief 析构函数
     */
    ~CalibrationManager();
    
    /**
     * @brief 初始化标定管理器
     * @return 初始化是否成功
     */
    bool initialize();
    
    /**
     * @brief 执行完整的标定流程
     * @param input_image_path 输入图像路径
     * @param output_path 输出路径
     * @return 标定结果错误码
     */
    ErrorCode performCalibration(const std::string& input_image_path,const std::string& output_path, const std::string& debug_path);
    
    /**
     * @brief 执行前向标定
     * @param input_image_path 输入图像路径
     * @param output_path 输出路径
     * @param calibrated_image 输出标定后的图像
     * @return 标定结果错误码
     */
    ErrorCode performForwardCalibration(const std::string& input_image_path,
                                       const std::string& output_path, const std::string& debug_path,
                                       cv::Mat& calibrated_image);
    
    /**
     * @brief 执行特征点检测和标定数据生成
     * @param calibrated_image 标定后的图像
     * @param output_path 输出路径
     * @return 标定结果错误码
     */
    ErrorCode performFeatureDetectionAndMapping(const cv::Mat& calibrated_image,
                                               const std::string& output_path, const std::string& debug_path);
    
    /**
     * @brief 生成标定查找表
     * @param csv_file CSV 数据文件路径
     * @param table_name 查找表名称
     * @param output_path 输出路径
     * @return 是否生成成功
     */
    bool generateCalibrationTable(const std::string& csv_file,
                                 const std::string& table_name,
                                 const std::string& output_path);

    /**
     * @brief 从CSV文件生成查找表（与原项目_generate_table完全一致）
     * @param csv_file CSV 数据文件路径
     * @param table_name 查找表名称
     * @param output_path 输出路径
     * @return 是否生成成功
     */
    bool generateTableFromCSV(const std::string& csv_file,
                             const std::string& table_name,
                             const std::string& output_path);

    /**
     * @brief 查找表插值处理和二进制输出（与原项目完全一致）
     * @param loc 坐标数组
     * @param table_height 表高度
     * @param table_width 表宽度
     * @param IMGX_RANGE X范围
     * @param IMGY_RANGE Y范围
     * @param X_MIN X最小值
     * @param X_MAX X最大值
     * @param Y_MIN Y最小值
     * @param Y_MAX Y最大值
     * @param file 二进制文件流
     * @param log 日志文件流
     * @return 是否处理成功
     */
    bool generateTableWithInterpolationAndOutput(float* loc, int table_height, int table_width,
                                                int IMGX_RANGE[2], int IMGY_RANGE[2],
                                                int X_MIN, int X_MAX, int Y_MIN, int Y_MAX,
                                                std::ofstream& file, std::ofstream& log, const std::string& output_path);

    /**
     * @brief 查找表最终处理和二进制输出（与原项目完全一致）
     */
    bool finalizeLookupTable(float* loc, int table_height, int table_width,
                           int X_MIN, int X_MAX, int Y_MIN, int Y_MAX,
                           std::ofstream& file, std::ofstream& log, const std::string& output_path);

    /**
     * @brief 查找行中左侧第一个非零列（与原项目find_Nozero3完全一致）
     */
    int findNonZeroLeft(float* loc_temp, int pixel_row, int table_width,
                       int IMGX_RANGE[2], int IMGY_RANGE[2]);

    /**
     * @brief 查找行中右侧最后一个非零列（与原项目find_Nozero4完全一致）
     */
    int findNonZeroRight(float* loc_temp, int pixel_row, int table_width,
                        int IMGX_RANGE[2], int IMGY_RANGE[2]);

    /**
     * @brief 检查列连通性（与原项目colTocol完全一致）
     */
    bool checkColumnConnectivity(float* loc_temp, int row, int col1, int col2,
                                int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]);

    /**
     * @brief 坐标量化函数（与原项目calc_loc完全一致）
     */
    int calcLoc(double loc, int s_min, int s_max);

    /**
     * @brief 从CSV文件加载标定数据（与原项目Calibration::read_from_csv完全一致）
     */
    bool loadCalibrationDataFromCSV(const std::string& csv_file);

    /**
     * @brief 像素坐标到世界坐标映射（与原项目Calibration::map_pixel_to_location完全一致）
     */
    core::CalibrationResult mapPixelToWorldOriginal(const core::Point2D& pixel_point);

    /**
     * @brief 与原项目完全一致的数据结构
     */
    struct PointInfo {
        double x;
        double y;
        PointInfo() : x(0), y(0) {}
        PointInfo(double x_val, double y_val) : x(x_val), y(y_val) {}
    };

    /**
     * @brief 计算叉积（与原项目cross_product完全一致）
     */
    double crossProduct(const PointInfo& p1, const PointInfo& p2, const core::Point2D& p3);

    /**
     * @brief 计算三角形面积（与原项目cal_area完全一致）
     */
    double calculateArea(const PointInfo& p1, const PointInfo& p2, const core::Point2D& p3);
    


    /**
     * @brief 获取标定统计信息
     */
    struct CalibrationStats {
        bool validation_passed;
        std::string error_message;
    };
    
    CalibrationStats getLastCalibrationStats() const { return last_stats_; }
    
    /**
     * @brief 设置调试模式
     * @param enable 是否启用调试模式
     */
    void setDebugMode(bool enable) { debug_mode_ = enable; }
    
    /**
     * @brief 获取调试模式状态
     */
    bool isDebugMode() const { return debug_mode_; }
    
private:
    // 初始化各个模块
    bool initializeModules();

    // 计算网格维度（从配置文件）
    bool calculateGridDimensions();

    // 初始化网格数据结构
    bool initializeGridDataStructures();

    // 清理资源
    void cleanup();
    
    // 验证输入参数
    bool validateInputs(const std::string& input_path, const std::string& output_path);
    
    // 创建输出目录
    bool createOutputDirectory(const std::string& output_path);

    // 保存增强图像 (与saveDebugImage保存方式统一)
    void saveEnhancedImage(const cv::Mat& image, const std::string& filename,
                          const std::string& output_path);

    // 注意：日志记录使用统一的LOG_*_FUNC宏，不需要私有日志函数

    // 重构后的子函数声明
    struct TableBounds {
        int minrow, mincol, maxrow, maxcol;
        TableBounds() : minrow(-1), mincol(-1), maxrow(-1), maxcol(-1) {}
    };

    bool findValidDataBounds(float* loc, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2], TableBounds& bounds);
    bool performRowwiseInterpolation(float* loc, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]);
    void interpolateLeftHalf(float* loc, int row, int col1, int col2, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]);
    void interpolateRightHalf(float* loc, int row, int col1, int col2, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]);
    
    // 成员变量
    ConfigManager& config_manager_;
    
    // 各个处理模块
    std::unique_ptr<calibration::ForwardCalibrator> forward_calibrator_;
    std::unique_ptr<processing::ImageEnhancer> image_enhancer_;
    std::unique_ptr<processing::FeatureDetector> feature_detector_;
    std::unique_ptr<processing::CoordinateMapper> coordinate_mapper_;
    
    // 状态变量
    bool initialized_;
    bool debug_mode_;
    CalibrationStats last_stats_;
    
    // 缓存的配置参数
    CameraIntrinsics camera_intrinsics_;
    DistortionCoefficients distortion_coeffs_;
    ImageProcessingParams processing_params_;
    BlobDetectorParams blob_params_;



    // 与原项目完全一致的网格数据
    std::vector<std::vector<PointInfo>> pixels3D_;  // 像素坐标网格
    std::vector<std::vector<PointInfo>> poses3D_;   // 世界坐标网格

    // 网格参数（与原项目完全一致）
    int COLS_;
    int ROWS_;
    double DIS_TO_CAMERA_;
    double DIS_TO_CAMERA2CENTER_;
    std::vector<double> h_axis_;
    std::vector<double> w_axis_;
};

} // namespace core
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_CORE_CALIBRATION_MANAGER_H
