[{"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/core/calibration_manager.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/core/calibration_manager.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/core/calibration_manager.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/core/config_manager.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/core/config_manager.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/core/config_manager.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/core/types.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/core/types.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/core/types.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/processing/coordinate_mapper.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/processing/coordinate_mapper.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/processing/coordinate_mapper.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/processing/feature_detector.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/processing/feature_detector.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/processing/feature_detector.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/processing/image_enhancer.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/processing/image_enhancer.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/processing/image_enhancer.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/processing/msrcr.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/processing/msrcr.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/processing/msrcr.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/calibration/forward_calibrator.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/calibration/forward_calibrator.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/calibration/forward_calibrator.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/utils/common_utils.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/utils/common_utils.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/utils/common_utils.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/utils/file_utils.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/utils/file_utils.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/utils/file_utils.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/utils/map_computer.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/utils/map_computer.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/utils/map_computer.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration_lib.dir/src/utils/opencv_utils.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/utils/opencv_utils.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/utils/opencv_utils.cpp"}, {"directory": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "command": "/usr/bin/g++  -I/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include -isystem /usr/local/include/opencv4 -g -O0 -Wall -Wextra -std=c++17 -o CMakeFiles/camera_calibration.dir/src/main.cpp.o -c /home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/main.cpp", "file": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/src/main.cpp"}]