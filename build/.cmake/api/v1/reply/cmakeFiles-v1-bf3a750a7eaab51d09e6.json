{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.22.1/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "/usr/local/lib/cmake/opencv4/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "/usr/local/lib/cmake/opencv4/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/usr/local/lib/cmake/opencv4/OpenCVModules.cmake"}, {"isExternal": true, "path": "/usr/local/lib/cmake/opencv4/OpenCVModules-release.cmake"}, {"isExternal": true, "path": "/home/<USER>/anaconda3/lib/cmake/yaml-cpp/yaml-cpp-config-version.cmake"}, {"isExternal": true, "path": "/home/<USER>/anaconda3/lib/cmake/yaml-cpp/yaml-cpp-config.cmake"}, {"isExternal": true, "path": "/home/<USER>/anaconda3/lib/cmake/yaml-cpp/yaml-cpp-targets.cmake"}, {"isExternal": true, "path": "/home/<USER>/anaconda3/lib/cmake/yaml-cpp/yaml-cpp-targets-release.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "source": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop"}, "version": {"major": 1, "minor": 0}}