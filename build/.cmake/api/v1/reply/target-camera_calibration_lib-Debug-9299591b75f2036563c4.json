{"archive": {}, "artifacts": [{"path": "lib/libcamera_calibration_lib.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "include_directories", "target_link_libraries"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 47, "parent": 0}, {"command": 1, "file": 0, "line": 85, "parent": 0}, {"command": 2, "file": 0, "line": 28, "parent": 0}, {"command": 3, "file": 0, "line": 56, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -O0 -Wall -Wextra"}, {"fragment": "-std=c++17"}], "includes": [{"backtrace": 3, "path": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include"}, {"backtrace": 3, "isSystem": true, "path": "/usr/local/include/opencv4"}], "language": "CXX", "languageStandard": {"backtraces": [4], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "id": "camera_calibration_lib::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/usr/local"}}, "name": "camera_calibration_lib", "nameOnDisk": "libcamera_calibration_lib.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/core/calibration_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/core/config_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/core/types.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/processing/coordinate_mapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/processing/feature_detector.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/processing/image_enhancer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/processing/msrcr.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/calibration/forward_calibrator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils/common_utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils/file_utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils/map_computer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils/opencv_utils.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}