{"artifacts": [{"path": "bin/camera_calibration"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 62, "parent": 0}, {"command": 1, "file": 0, "line": 81, "parent": 0}, {"command": 2, "file": 0, "line": 65, "parent": 0}, {"command": 3, "file": 0, "line": 28, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -O0 -Wall -Wextra"}, {"fragment": "-std=c++17"}], "includes": [{"backtrace": 4, "path": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/include"}, {"backtrace": 4, "isSystem": true, "path": "/usr/local/include/opencv4"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "camera_calibration_lib::@6890427a1f51a3e7e1df"}], "id": "camera_calibration::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-g -O0 -Wall -Wextra", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/usr/local/lib:", "role": "libraries"}, {"backtrace": 3, "fragment": "lib/libcamera_calibration_lib.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_gapi.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_highgui.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_ml.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_objdetect.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_photo.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_stitching.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_video.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_videoio.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "-lyaml-cpp", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_calib3d.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_dnn.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_features2d.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_flann.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_imgcodecs.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_imgproc.so.4.12.0", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libopencv_core.so.4.12.0", "role": "libraries"}], "language": "CXX"}, "name": "camera_calibration", "nameOnDisk": "camera_calibration", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}