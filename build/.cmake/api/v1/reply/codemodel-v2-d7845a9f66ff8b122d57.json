{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-12961f54c31b6908e8dc.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "camera_calibration_refactored", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "camera_calibration::@6890427a1f51a3e7e1df", "jsonFile": "target-camera_calibration-Debug-450727a9ba6e1bcb0367.json", "name": "camera_calibration", "projectIndex": 0}, {"directoryIndex": 0, "id": "camera_calibration_lib::@6890427a1f51a3e7e1df", "jsonFile": "target-camera_calibration_lib-Debug-9299591b75f2036563c4.json", "name": "camera_calibration_lib", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop/build", "source": "/home/<USER>/panpan/code/Calib/camera_calib-main_org_laptop"}, "version": {"major": 2, "minor": 3}}