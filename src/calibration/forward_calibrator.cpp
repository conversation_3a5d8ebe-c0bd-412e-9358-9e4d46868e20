#include "calibration/forward_calibrator.h"
#include "utils/file_utils.h"
#include "utils/map_computer.h"
#include "utils/common_utils.h"
#include "utils/opencv_utils.h"
#include "core/constants.h"
#include <iostream>

namespace camera_calibration {
namespace calibration {

ForwardCalibrator::ForwardCalibrator()
    : initialized_(false)
    , debug_mode_(false)
    , maps_initialized_(false) {
}

ForwardCalibrator::~ForwardCalibrator() {
    // 清理资源
}

bool ForwardCalibrator::initialize(const core::CameraIntrinsics& intrinsics,
                                  const core::DistortionCoefficients& dist_coeffs,
                                  const core::ImageProcessingParams& processing_params) {
    try {
        // 保存参数
        camera_intrinsics_ = intrinsics;
        distortion_coeffs_ = dist_coeffs;
        processing_params_ = processing_params;
        
        // 创建 OpenCV 相机矩阵
        camera_matrix_ = intrinsics.toCameraMatrix();
        dist_coeffs_mat_ = dist_coeffs.toDistCoeffs();
        
        // 设置棋盘格参数
        chessboard_size_ = cv::Size(processing_params.chess_cols, processing_params.chess_rows);
        chessboard_bounds_ = processing_params.chess_bounds;
        
        initialized_ = true;
        LOG_INFO_FUNC("ForwardCalibrator", "前向标定器初始化成功");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "前向标定器初始化失败: " + std::string(e.what()));
        return false;
    }
}

core::ErrorCode ForwardCalibrator::performCalibration(const std::string& input_image_path,
                                                     const std::string& output_path, const std::string& debug_path,
                                                     cv::Mat& calibrated_image) {
    if (!initialized_) {
        LOG_ERROR_FUNC("ForwardCalibrator", "前向标定器未初始化");
        return core::ErrorCode::INVALID_PARAMETERS;
    }

    try {
        
        LOG_INFO_FUNC("ForwardCalibrator", "开始前向标定: " + input_image_path);

        // 准备相机参数矩阵
        double mtx[3][3] = {{0}};
        double dist[8] = {0};

        // 从相机内参和畸变系数填充矩阵
        mtx[0][0] = camera_intrinsics_.fx;
        mtx[0][1] = 0;
        mtx[0][2] = camera_intrinsics_.cx;
        mtx[1][0] = 0;
        mtx[1][1] = camera_intrinsics_.fy;
        mtx[1][2] = camera_intrinsics_.cy;
        mtx[2][0] = 0;
        mtx[2][1] = 0;
        mtx[2][2] = 1;

        dist[0] = distortion_coeffs_.k1;
        dist[1] = distortion_coeffs_.k2;
        dist[2] = distortion_coeffs_.p1;
        dist[3] = distortion_coeffs_.p2;
        dist[4] = distortion_coeffs_.k3;
        dist[5] = distortion_coeffs_.k4;
        dist[6] = distortion_coeffs_.k5;
        dist[7] = distortion_coeffs_.k6;

        // 调用与原项目完全一致的 forwardChessCalibration 函数
        int result = forwardChessCalibration(input_image_path.c_str(),  output_path, debug_path,
                                           mtx, dist, calibrated_image);

        if (result != 0) {
            LOG_ERROR_FUNC("ForwardCalibrator", "前向棋盘格标定失败，错误代码: " + std::to_string(result));
            last_stats_.status_message = "前向棋盘格标定失败";
            return core::ErrorCode::FORWARD_CALIB_FAILED;
        }

        // 更新统计信息
        last_stats_.corners_detected = true;
        last_stats_.image_size = calibrated_image.size();
        last_stats_.status_message = "前向标定成功完成";

        LOG_INFO_FUNC("ForwardCalibrator", "前向标定完成，结果保存至: " + output_path  + constants::standard_filenames::FINAL_CALIBRATED_IMAGE);
        return core::ErrorCode::SUCCESS;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "OpenCV 异常: " + std::string(e.what()));
        last_stats_.status_message = "OpenCV 异常: " + std::string(e.what());
        return core::ErrorCode::FORWARD_CALIB_FAILED;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "前向标定异常: " + std::string(e.what()));
        last_stats_.status_message = "前向标定异常: " + std::string(e.what());
        return core::ErrorCode::FORWARD_CALIB_FAILED;
    }
}

bool ForwardCalibrator::detectChessboard(const cv::Mat& image, std::vector<cv::Point2f>& corners, const std::string& debug_path) {
    try {
        // LOG_INFO_FUNC("ForwardCalibrator", "Start find Chessboard...");

        // 棋盘格模式尺寸，注意：原项目中 CHESS_ROWS 对应 cols，CHESS_COLS 对应 rows
        cv::Size patternSize(processing_params_.chess_rows, processing_params_.chess_cols);

        LOG_DEBUG_FUNC("ForwardCalibrator", "棋盘格检测尺寸: " + std::to_string(processing_params_.chess_rows) + "x" +
                std::to_string(processing_params_.chess_cols), debug_mode_);

        // 使用 OpenCV 函数查找棋盘格角点
        bool found = cv::findChessboardCorners(image, patternSize, corners);

        // 如果未找到角点，输出提示信息并返回
        if (!found) {
            LOG_ERROR_FUNC("ForwardCalibrator", "Cannot find chessboard corners！ 未找到棋盘格边缘！");
            return false;
        }

        // 程序应当只在找到角点的情况下运行到这里
        assert(found);

        // 在图像上绘制找到的角点
        cv::Mat corner_image = image.clone();
        cv::drawChessboardCorners(corner_image, patternSize, corners, found);

        // 保存绘制角点后的图像，用于验证角点提取效果
        saveDebugImage(corner_image, constants::standard_filenames::CHESSBOARD_IMAGE, debug_path);

        // LOG_INFO_FUNC("ForwardCalibrator", "棋盘格角点检测成功，共 " + std::to_string(corners.size()) + " 个角点");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "棋盘格角点检测异常: " + std::string(e.what()));
        return false;
    }
}

bool ForwardCalibrator::getChessWarpMatrix(const std::vector<cv::Point2f>& corners, cv::Mat& warpM, const std::string& output_path) {
    try {
        LOG_INFO_FUNC("ForwardCalibrator", "开始计算透视变换矩阵...");

        std::vector<cv::Point2f> dst_points;

        // 从配置中读取棋盘格在目标图像上的映射坐标
        cv::Point2f upper_left(processing_params_.chess_bounds.x, processing_params_.chess_bounds.y);
        cv::Point2f lower_right(processing_params_.chess_bounds.x + processing_params_.chess_bounds.width,
                               processing_params_.chess_bounds.y + processing_params_.chess_bounds.height);
        std::cout << "width: " <<  processing_params_.chess_bounds.width << std::endl;
        std::cout << "height: " << processing_params_.chess_bounds.height << std::endl; 
        LOG_DEBUG_FUNC("ForwardCalibrator", "棋盘格边界: (" + std::to_string(processing_params_.chess_bounds.x) + "," +
                std::to_string(processing_params_.chess_bounds.y) + ") 到 (" +
                std::to_string(processing_params_.chess_bounds.x + processing_params_.chess_bounds.width) + "," +
                std::to_string(processing_params_.chess_bounds.y + processing_params_.chess_bounds.height) + ")", debug_mode_);

        // 从配置中读取棋盘格列数和行数
        int cols = processing_params_.chess_rows;
        int rows = processing_params_.chess_cols;

        LOG_DEBUG_FUNC("ForwardCalibrator", "棋盘格尺寸: " + std::to_string(cols) + "x" + std::to_string(rows), debug_mode_);

        // 每个点在目标图像上的水平和垂直间隔
        float col_step = (lower_right.x - upper_left.x) / (cols - 1);
        float row_step = (lower_right.y - upper_left.y) / (rows - 1);

        // 构建目标图像上的角点位置
        for (int row = 0; row < rows; row++) {
            for (int col = 0; col < cols; col++) {
                dst_points.emplace_back(
                    cv::Point2f(upper_left.x + col * col_step,
                               upper_left.y + row * row_step));
            }
        }

        // 计算透视变换矩阵：将实际检测到的角点映射到理想位置
        warpM = cv::findHomography(corners, dst_points);

        if (warpM.empty()) {
            LOG_ERROR_FUNC("ForwardCalibrator", "透视变换矩阵计算失败");
            return false;
        }

        std::vector<cv::Point2f> projected_corners;
        cv::perspectiveTransform(corners, projected_corners, warpM);
        float error = cv::norm(projected_corners, dst_points, cv::NORM_L2) / corners.size();
        LOG_INFO_FUNC("ForwardCalibrator", "透视变换矩阵计算完成，重投影误差: " + std::to_string(error));
        
        std::string warp_path = output_path + "/" + constants::standard_filenames::WARPED_FILE;
        std::ofstream outFile(warp_path);
        outFile << warpM << std::endl;
        outFile.close();
        

        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "计算透视变换矩阵异常: " + std::string(e.what()));
        return false;
    }
}

int ForwardCalibrator::getBottom(const cv::Mat& calibed_image) {
    try {
        // LOG_INFO_FUNC("ForwardCalibrator", "Start finding blind area...");

        // 储存各列盲区起始位置（即白色区域的最上方像素行数）
        std::vector<int> bottoms = {0};

        int center = processing_params_.src_width / 2;  // 图像中心列位置
        for (int col = center - constants::BLIND_AREA_DETECTION_RANGE; col < center + constants::BLIND_AREA_DETECTION_RANGE; ++col) {
            for (int row = 0; row < processing_params_.src_height; ++row) {
                
                // 自底向上遍历图像（从底部第0行开始计数）
                int pixel_value = calibed_image.at<uchar>(processing_params_.src_height - 1 - row, col);
                cv::Vec3b pixel = calibed_image.at<cv::Vec3b>(processing_params_.src_height - 1 - row, col);
                // std::cout << "pixel_value1: " << pixel << std::endl;
                
                // std::cout << "pixel: (" + std::to_string(col) + ", " + std::to_string(processing_params_.src_height - 1 - row) + ") = " + std::to_string(pixel_value) << std::endl;
                if (pixel[0] > 150 && pixel[1] > 150 && pixel[2] > 150) {
                    // std::cout << processing_params_.src_height - 1 - row << std::endl;
                    // std::cout << col << std::endl;
                    // std::cout << row << std::endl;
                    if (row > constants::MAX_BLIND_AREA_SIZE) {
                        LOG_ERROR_FUNC("ForwardCalibrator",
                            "Warning! Blind area too large at pixel("
                            + std::to_string(col) + ", "
                            + std::to_string(processing_params_.src_height - 1 - row) +  // OpenCV坐标(y)
                            + "), row=" + std::to_string(row) );
                        LOG_ERROR_FUNC("ForwardCalibrator", "Warning! Blind area seems too large, please check the image and camera!");
                        cv::circle(calibed_image, cv::Point(col, processing_params_.src_height - 1 - row), 3, cv::Scalar(0,0,255), -1);
                        cv::imwrite("test.png", calibed_image);
                        return -1;
                    }
                    bottoms.push_back(row);  // 找到白色点（盲区边界），保存后跳出本列遍历
                    break;
                }
            }
        }

        
        // 取所有检测列中的最大盲区高度（越大表示盲区越深）
        int bottom = *std::max_element(bottoms.begin(), bottoms.end());
        std::cout << "bottom: " << bottom << std::endl;
        // 加上配置文件中设置的偏移阈值，返回最终盲区底部位置
        int final_bottom = bottom + processing_params_.bottom_threshold;

        LOG_INFO_FUNC("ForwardCalibrator", "检测到盲区底部位置: " + std::to_string(final_bottom));
        return final_bottom;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "检测盲区异常: " + std::string(e.what()));
        return -1;
    }
}

bool ForwardCalibrator::getChessInvHomoMatrix(const std::vector<cv::Point2f>& corners, int bottom, cv::Mat& inv_homo) {
    try {
        // LOG_INFO_FUNC("ForwardCalibrator", "Start calculate invHomoMatrix...");

        // 图像截取区域大小（假设输入图像比输出图像高）
        int cut = processing_params_.src_height - processing_params_.output_height;

        // 构建目标图像中角点的目标位置
        std::vector<cv::Point2f> dst_points;

        // 棋盘格在目标图像上的左上和右下边界坐标
        cv::Point2f upper_left(processing_params_.chess_bounds.x, processing_params_.chess_bounds.y);
        cv::Point2f lower_right(processing_params_.chess_bounds.x + processing_params_.chess_bounds.width,
                               processing_params_.chess_bounds.y + processing_params_.chess_bounds.height);

        int cols = processing_params_.chess_rows; // 如 9：每行角点数量
        int rows = processing_params_.chess_cols; // 如 4：角点行数

        // 计算每列、每行之间的坐标间距
        float col_step = (lower_right.x - upper_left.x) / (cols - 1);
        float row_step = (lower_right.y - upper_left.y) / (rows - 1);

        // 根据 bottom 和 cut 值偏移目标点的位置
        for (int row = 0; row < rows; row++) {
            for (int col = 0; col < cols; col++) {
                dst_points.emplace_back(
                    cv::Point2f(upper_left.x + col * col_step,
                               upper_left.y + row * row_step - cut + bottom));
            }
        }

        // 计算正向透视矩阵（原图 → 校正图）
        cv::Mat homo = cv::findHomography(corners, dst_points);

        // 取其逆矩阵作为反向映射（校正图 → 原图）
        cv::invert(homo, inv_homo);

        if (inv_homo.empty()) {
            LOG_ERROR_FUNC("ForwardCalibrator", "反向透视变换矩阵计算失败");
            return false;
        }

        LOG_INFO_FUNC("ForwardCalibrator", "反向透视变换矩阵计算完成");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "计算反向透视变换矩阵异常: " + std::string(e.what()));
        return false;
    }
}



/**
 * @brief 初始化矫正图像畸变所需的映射表 mapx 和 mapy。
 *
 * 该函数基于相机内参矩阵和畸变系数，计算畸变矫正的映射表，供 remap() 函数使用。
 * 使用 OpenCV 的 getOptimalNewCameraMatrix 和 initUndistortRectifyMap 实现。
 *
 * @param mtx     相机内参矩阵（3x3）
 * @param dist    畸变系数数组（长度为8）
 * @param mapx    输出的 mapx 映射表（CV_32FC1 格式），用于 remap
 * @param mapy    输出的 mapy 映射表（CV_32FC1 格式），用于 remap
 * @param height  原始图像高度（像素）
 * @param width   原始图像宽度（像素）
 * @param ratio   新相机矩阵缩放比例，默认值为2，决定输出图像大小（不建议修改）
 */
void initUndistortMap(double mtx[3][3],    // 相机内参矩阵
                      double dist[8],      // 畸变参数
                      cv::Mat &mapx,       // 输出映射表 mapx
                      cv::Mat &mapy,       // 输出映射表 mapy
                      int height,          // 输入图像高度
                      int width,           // 输入图像宽度
                      int ratio = 2)       // 放大比例因子（影响输出尺寸）
{
    // std::cout << "Initializing distortion correction map..." << std::endl;

    // 将输入的内参数组转换为 OpenCV 的 Mat 类型
    cv::Mat cameraMatrix = utils::OpenCVUtils::createCameraMatrix(mtx);

    // 将输入的畸变参数数组转换为 Mat
    cv::Mat distCoeffs = utils::OpenCVUtils::createDistortionCoeffs(dist, constants::DISTORTION_COEFFS_SIZE);

    // 图像原始尺寸
    cv::Size imageSize(width, height);

    // 矫正后图像尺寸，根据 ratio 放大
    cv::Size newimageSize(width * ratio, height * ratio);

    // 获取新的优化相机内参矩阵（用于最大限度保留图像内容）
    cv::Mat newcameraMatrix = getOptimalNewCameraMatrix(
        cameraMatrix, distCoeffs, imageSize, 1, newimageSize);

    // 初始化映射表 mapx 和 mapy，供 remap() 使用
    // 参数说明：
    // - cameraMatrix: 原始相机矩阵
    // - distCoeffs: 畸变参数
    // - R: 校正变换矩阵（此处为单位矩阵）
    // - newCameraMatrix: 新的相机矩阵
    // - size: 输出图像尺寸
    // - mtype: 映射表数据类型，5 表示 CV_32FC1
    initUndistortRectifyMap(
        cameraMatrix, distCoeffs, cv::Mat(), newcameraMatrix, newimageSize,
        CV_32FC1, mapx, mapy);
}




void ForwardCalibrator::refineCorners(const cv::Mat& image, std::vector<cv::Point2f>& corners) {
    cv::cornerSubPix(image, corners,
                    cv::Size(static_cast<int>(constants::CORNER_SUB_PIX_WINDOW_SIZE),
                            static_cast<int>(constants::CORNER_SUB_PIX_WINDOW_SIZE)),
                    cv::Size(-1, -1),
                    utils::OpenCVUtils::createCornerSubPixCriteria());
}



void ForwardCalibrator::postProcess(cv::Mat& image) {
    try {
        std::vector<cv::Point2f> corners;
        // 设置棋盘格的规格（行数和列数）
        cv::Size patternSize = cv::Size(processing_params_.chess_rows, processing_params_.chess_cols);
        // std::cout<<"processing_params_.chess_rows"<<processing_params_.chess_rows<<std::endl;
        // 在图像中寻找棋盘格角点，返回是否找到
        bool found = cv::findChessboardCorners(image, patternSize, corners);

        if (found && !corners.empty()) {
            // 如果找到角点，则获取最后一个角点的y坐标，并加上一个偏移量，用于确定矩形的纵向边界
            int chess_y_max = static_cast<int>(corners.back().y + processing_params_.chess_y_offset);

            // 在图像的左上角到 (SRC_W, chessYMax) 区域绘制一个纯白色矩形，用于遮盖该区域
            cv::rectangle(image, cv::Point(0, 0),
                         cv::Point(processing_params_.src_width, chess_y_max),
                         cv::Scalar(constants::WHITE_PIXEL_VALUE, constants::WHITE_PIXEL_VALUE, constants::WHITE_PIXEL_VALUE), -1, 8, 0);

            LOG_DEBUG_FUNC("ForwardCalibrator", "后处理完成，遮盖区域高度: " + std::to_string(chess_y_max), debug_mode_);
        } else {
            LOG_DEBUG_FUNC("ForwardCalibrator", "后处理时未找到棋盘格角点，跳过遮盖操作", debug_mode_);
        }

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "图像后处理异常: " + std::string(e.what()));
    }
}

void ForwardCalibrator::postProcessImage(cv::Mat& image) {
    postProcess(image);
}

void ForwardCalibrator::saveDebugImage(const cv::Mat& image, const std::string& filename,
                                      const std::string& output_path) {
    if (!debug_mode_) return;

    // 使用统一的图像保存方式
    if (utils::FileOperationHelper::saveImageSafely(image, filename, output_path, "ForwardCalibrator")) {
        LOG_DEBUG_FUNC("ForwardCalibrator", "调试图像已保存: " + output_path + filename, debug_mode_);
    } else {
        LOG_ERROR_FUNC("ForwardCalibrator", "保存调试图像失败: " + output_path + "/" + filename);
    }
}



// 添加与原项目完全一致的 initCalibMap 函数
void ForwardCalibrator::initCalibMap(double mtx[3][3], double dist[8], const cv::Mat& HomoI,
                                    cv::Mat& mapx, cv::Mat& mapy, int height, int width,
                                    cv::Size outSize, int ratio) {
    try {
        // LOG_INFO_FUNC("ForwardCalibrator", "Initializing calibration map...");

        // 与原项目完全一致的实现
        cv::Mat cameraMatrix(cv::Size(3, 3), CV_64F, mtx);
        cv::Mat distCoeffs(cv::Size(8, 1), CV_64F, dist);

        cv::Size imageSize(width, height);
        cv::Size newimageSize(width * ratio, height * ratio);
        cv::Mat newcameraMatrix = cv::getOptimalNewCameraMatrix(cameraMatrix, distCoeffs, imageSize, 1, newimageSize);

        LOG_INFO_FUNC("ForwardCalibrator", "开始计算Mapx和Mapy");
        initCalibRectifyMap(cameraMatrix, distCoeffs, HomoI, cv::Mat(), newcameraMatrix, outSize, CV_32FC1, mapx, mapy);

        LOG_INFO_FUNC("ForwardCalibrator", "标定映射表初始化完成");

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "初始化标定映射表异常: " + std::string(e.what()));
    }
}

/*-------将mapx mapy的Mat存储为二进制文件---------*/
bool ForwardCalibrator::matWrite(std::string filename, cv::Mat& _M)
{
    cv::Mat M;
    _M.copyTo(M);
    FILE* file = fopen(filename.data(), "wb");
    if (file == NULL || M.empty())
        return false;
    //cout<<"data type:"<<M.type()<<endl;
    fwrite(M.data, sizeof(char), M.step * M.rows, file);
    fclose(file);
    return true;
}

// 重构后的 forwardChessCalibration 主控制函数
int ForwardCalibrator::forwardChessCalibration(const char* imgpath, const std::string& output_path, const std::string& debug_path,
                                              double mtx[3][3], double dist[8],
                                              cv::Mat& outImg) {
    try {
        // 1. 加载和验证输入图像
        cv::Mat srcImg;
        if (!loadAndValidateImage(imgpath, srcImg, debug_path)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "图像加载失败");
            return -1;
        }

        // 2. 执行去畸变处理
        cv::Mat undistortImg;
        if (!performUndistortion(srcImg, undistortImg, mtx, dist, debug_path)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "去畸变处理失败");
            return -1;
        }

        // 3. 检测和处理棋盘格
        std::vector<cv::Point2f> corners;
        if (!detectAndProcessChessboard(undistortImg, corners, debug_path)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "棋盘格检测失败");
            return -1;
        }

        // // 4. 执行透视变换
        cv::Mat warpImg, warpM;
        if (!performPerspectiveTransform(undistortImg, warpImg, corners, warpM, output_path, debug_path)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "透视变换失败");
            return -1;
        }
        // warpImg = undistortImg;
        // 5. 检测盲区
        int bottom;
        if (!detectBlindArea(warpImg, bottom)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "盲区检测失败");
            return -1;
        }

        // 6. 生成标定映射
        cv::Mat Mapx, Mapy;
        if (!generateCalibrationMaps(corners, bottom, mtx, dist, Mapx, Mapy, output_path)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "标定映射生成失败");
            return -1;
        }

        // 7. 应用最终标定
        if (!applyFinalCalibration(srcImg, outImg, Mapx, Mapy, debug_path)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "最终标定应用失败");
            return -1;
        }

        LOG_INFO_FUNC("ForwardCalibrator", "前向棋盘格标定完成");
        return 0;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "前向棋盘格标定异常: " + std::string(e.what()));
        return -1;
    }
}

// 重构后的子函数实现

bool ForwardCalibrator::loadAndValidateImage(const char* imgpath, cv::Mat& srcImg, const std::string& output_path) {
    try {
        // 读取输入图像
        srcImg = cv::imread(imgpath, -1);
        if (!srcImg.data) {
            LOG_ERROR_FUNC("ForwardCalibrator", "未找到原图，请确认路径: " + std::string(imgpath));
            return false;
        }

        // 验证图像尺寸
        if (srcImg.cols != processing_params_.src_width || srcImg.rows != processing_params_.src_height) {
            LOG_INFO_FUNC("ForwardCalibrator", "图像尺寸与配置不匹配: " +
                           std::to_string(srcImg.cols) + "x" + std::to_string(srcImg.rows) +
                           " vs " + std::to_string(processing_params_.src_width) + "x" +
                           std::to_string(processing_params_.src_height));
        }

        // 保存原图用于调试
        saveDebugImage(srcImg, constants::standard_filenames::ORIGINAL_IMAGE, output_path);

        LOG_INFO_FUNC("ForwardCalibrator", "图像加载成功: " + std::to_string(srcImg.cols) + "x" +
                     std::to_string(srcImg.rows) + ", 通道数: " + std::to_string(srcImg.channels()));
        return true;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "OpenCV图像加载异常: " + std::string(e.what()));
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "图像加载异常: " + std::string(e.what()));
        return false;
    }
}

bool ForwardCalibrator::performUndistortion(const cv::Mat& srcImg, cv::Mat& undistortImg,
                                           double mtx[3][3], double dist[8], const std::string& debug_path) {
    try {
        LOG_INFO_FUNC("ForwardCalibrator", "开始去畸变");
        // 初始化去畸变映射表
        cv::Mat mapx, mapy;
        initUndistortMap(mtx, dist, mapx, mapy, processing_params_.src_height, processing_params_.src_width);

        // 执行去畸变
        cv::remap(srcImg, undistortImg, mapx, mapy, cv::INTER_LINEAR);

        // 验证去畸变结果
        if (undistortImg.empty()) {
            LOG_ERROR_FUNC("ForwardCalibrator", "去畸变结果为空");
            return false;
        }

        // 保存去畸变图像用于调试
        saveDebugImage(undistortImg, constants::standard_filenames::UNDISTORTED_IMAGE, debug_path);

        LOG_INFO_FUNC("ForwardCalibrator", "去畸变处理完成");
        return true;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "OpenCV去畸变异常: " + std::string(e.what()));
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "去畸变处理异常: " + std::string(e.what()));
        return false;
    }
}

bool ForwardCalibrator::detectAndProcessChessboard(const cv::Mat& undistortImg, std::vector<cv::Point2f>& corners, const std::string& debug_path) {
    try {
        // 检测棋盘格角点
        if (!detectChessboard(undistortImg, corners, debug_path)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "棋盘格角点检测失败");
            return false;
        }

        // 验证角点数量
        int expected_corners = processing_params_.chess_rows * processing_params_.chess_cols;
        if (static_cast<int>(corners.size()) != expected_corners) {
            LOG_ERROR_FUNC("ForwardCalibrator", "角点数量不匹配，期望: " + std::to_string(expected_corners) +
                          ", 实际: " + std::to_string(corners.size()));
            return false;
        }

        LOG_INFO_FUNC("ForwardCalibrator", "棋盘格检测成功，角点数量: " + std::to_string(corners.size()));
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "棋盘格检测异常: " + std::string(e.what()));
        return false;
    }
}

bool ForwardCalibrator::performPerspectiveTransform(const cv::Mat& undistortImg, cv::Mat& warpImg,
                                                   const std::vector<cv::Point2f>& corners,
                                                   cv::Mat& warpM, const std::string& outfile, const std::string& debug_path) {
    try {
        // 计算透视变换矩阵
        if (!getChessWarpMatrix(corners, warpM, outfile)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "计算透视变换矩阵失败");
            return false;
        }

        // 验证变换矩阵
        if (warpM.empty() || warpM.rows != 3 || warpM.cols != 3) {
            LOG_ERROR_FUNC("ForwardCalibrator", "透视变换矩阵无效");
            return false;
        }
        
        // 应用透视变换
        cv::warpPerspective(undistortImg, warpImg, warpM,
                           cv::Size(processing_params_.src_width, processing_params_.src_height));

        // 验证变换结果
        if (warpImg.empty()) {
            LOG_ERROR_FUNC("ForwardCalibrator", "透视变换结果为空");
            return false;
        }

        // 保存透视变换图像用于调试
        saveDebugImage(warpImg, constants::standard_filenames::WARPED_IMAGE, debug_path);

        LOG_INFO_FUNC("ForwardCalibrator", "透视变换完成");
        return true;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "OpenCV透视变换异常: " + std::string(e.what()));
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "透视变换异常: " + std::string(e.what()));
        return false;
    }
}

bool ForwardCalibrator::detectBlindArea(const cv::Mat& warpImg, int& bottom) {
    try {
        // 检测盲区
        bottom = getBottom(warpImg);

        // 验证盲区检测结果
        if (bottom == -1) {
            LOG_ERROR_FUNC("ForwardCalibrator", "盲区检测失败");
            return false;
        }

        // 验证盲区位置的合理性
        if (bottom < 0 || bottom >= warpImg.rows) {
            LOG_ERROR_FUNC("ForwardCalibrator", "盲区位置无效: " + std::to_string(bottom) +
                          ", 图像高度: " + std::to_string(warpImg.rows));
            return false;
        }

        LOG_INFO_FUNC("ForwardCalibrator", "盲区检测完成");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "盲区检测异常: " + std::string(e.what()));
        return false;
    }
}

bool ForwardCalibrator::generateCalibrationMaps(const std::vector<cv::Point2f>& corners, int bottom,
                                               double mtx[3][3], double dist[8],
                                               cv::Mat& Mapx, cv::Mat& Mapy, const std::string& output_path) {
    try {
        LOG_INFO_FUNC("ForwardCalibrator", "开始计算反向透视变换矩阵...");

        // 计算反向透视变换矩阵
        cv::Mat invHomoM;
        if (!getChessInvHomoMatrix(corners, bottom, invHomoM)) {
            LOG_ERROR_FUNC("ForwardCalibrator", "反向透视变换矩阵计算失败");
            return false;
        }

        // 验证反向变换矩阵
        if (invHomoM.empty() || invHomoM.rows != 3 || invHomoM.cols != 3) {
            LOG_ERROR_FUNC("ForwardCalibrator", "反向透视变换矩阵无效");
            return false;
        }

        // 生成最终的映射表
        cv::Size output_size(processing_params_.src_width, processing_params_.src_height);
        initCalibMap(mtx, dist, invHomoM, Mapx, Mapy,
                    processing_params_.src_height, processing_params_.src_width, output_size);

        // 验证映射表
        if (Mapx.empty() || Mapy.empty()) {
            LOG_ERROR_FUNC("ForwardCalibrator", "标定映射表生成失败");
            return false;
        }

        // 保存映射表
        matWrite(output_path + constants::standard_filenames::MAPX_FILE, Mapx);
        matWrite(output_path + constants::standard_filenames::MAPY_FILE, Mapy);

        LOG_INFO_FUNC("ForwardCalibrator", "标定映射生成完成，映射表尺寸: " +
                     std::to_string(Mapx.cols) + "x" + std::to_string(Mapx.rows));
        return true;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "OpenCV映射生成异常: " + std::string(e.what()));
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "标定映射生成异常: " + std::string(e.what()));
        return false;
    }
}

bool ForwardCalibrator::applyFinalCalibration(const cv::Mat& srcImg, cv::Mat& outImg,
                                             const cv::Mat& Mapx, const cv::Mat& Mapy,
                                             const std::string& outfile) {
    try {
        // 验证输入参数
        if (srcImg.empty()) {
            LOG_ERROR_FUNC("ForwardCalibrator", "源图像为空");
            return false;
        }

        if (Mapx.empty() || Mapy.empty()) {
            LOG_ERROR_FUNC("ForwardCalibrator", "映射表为空");
            return false;
        }

        // 验证映射表尺寸
        if (Mapx.size() != srcImg.size() || Mapy.size() != srcImg.size()) {
            LOG_ERROR_FUNC("ForwardCalibrator", "映射表尺寸与源图像不匹配");
            return false;
        }

        // 应用最终映射
        cv::Mat finalCalibedImg;
        cv::remap(srcImg, finalCalibedImg, Mapx, Mapy, cv::INTER_LINEAR);

        // 验证标定结果
        if (finalCalibedImg.empty()) {
            LOG_ERROR_FUNC("ForwardCalibrator", "最终标定结果为空");
            return false;
        }

        // 保存中间结果用于调试
        saveDebugImage(finalCalibedImg, constants::standard_filenames::beforepost_CALIBRATED_IMAGE, outfile);

        // 后处理
        postProcess(finalCalibedImg);

        // 输出最终结果
        outImg = finalCalibedImg;
        saveDebugImage(finalCalibedImg, constants::standard_filenames::FINAL_CALIBRATED_IMAGE, outfile);

        // LOG_INFO_FUNC("ForwardCalibrator", "最终标定应用完成，输出图像尺寸: " +
        //              std::to_string(outImg.cols) + "x" + std::to_string(outImg.rows));
        return true;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "OpenCV最终标定异常: " + std::string(e.what()));
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ForwardCalibrator", "最终标定应用异常: " + std::string(e.what()));
        return false;
    }
}

} // namespace calibration
} // namespace camera_calibration
