#include "utils/opencv_utils.h"
#include "utils/common_utils.h"
#include "core/constants.h"
#include <iostream>
#include <cmath>

namespace camera_calibration {
namespace utils {

cv::Mat OpenCVUtils::createCameraMatrix(double mtx[3][3]) {
    cv::Mat camera_matrix(constants::CAMERA_MATRIX_ROWS, constants::CAMERA_MATRIX_COLS, CV_64F);
    
    for (int i = 0; i < constants::CAMERA_MATRIX_ROWS; ++i) {
        for (int j = 0; j < constants::CAMERA_MATRIX_COLS; ++j) {
            camera_matrix.at<double>(i, j) = mtx[i][j];
        }
    }
    
    return camera_matrix;
}

cv::Mat OpenCVUtils::createDistortionCoeffs(double dist[], int size) {
    cv::Mat dist_coeffs(1, size, CV_64F);
    
    for (int i = 0; i < size; ++i) {
        dist_coeffs.at<double>(0, i) = dist[i];
    }
    
    return dist_coeffs;
}

bool OpenCVUtils::saveImageSafely(const cv::Mat& image, 
                                 const std::string& path,
                                 const std::string& class_name) {
    if (!ImageValidator::validateImage(image, class_name)) {
        return false;
    }
    
    return executeOpenCVOperation(
        [&]() { 
            if (!cv::imwrite(path, image)) {
                throw std::runtime_error("cv::imwrite 返回 false");
            }
        },
        "保存图像到 " + path,
        class_name
    );
}

bool OpenCVUtils::loadImageSafely(const std::string& path,
                                 cv::Mat& image,
                                 int flags,
                                 const std::string& class_name) {
    return executeOpenCVOperation(
        [&]() {
            image = cv::imread(path, flags);
            if (image.empty()) {
                throw std::runtime_error("无法读取图像文件");
            }
        },
        "读取图像从 " + path,
        class_name
    );
}

bool OpenCVUtils::isWhitePixel(uint8_t pixel_value) {
    return pixel_value == constants::WHITE_PIXEL_VALUE;
}

// bool OpenCVUtils::isWhitePixel(cv::Vec3b &pixel) {
//     return (pixel[0] == constants::WHITE_PIXEL_VALUE && pixel[1] == constants::WHITE_PIXEL_VALUE && pixel[2] == constants::WHITE_PIXEL_VALUE);
// }


bool OpenCVUtils::isBlackPixel(uint8_t pixel_value) {
    return pixel_value == constants::BLACK_PIXEL_VALUE;
}

cv::TermCriteria OpenCVUtils::createCornerSubPixCriteria() {
    return cv::TermCriteria(
        cv::TermCriteria::EPS + cv::TermCriteria::COUNT,
        constants::CORNER_SUB_PIX_MAX_ITERATIONS,
        constants::CORNER_SUB_PIX_EPSILON
    );
}

bool OpenCVUtils::validateMatrix(const cv::Mat& matrix,
                                int expected_rows,
                                int expected_cols,
                                const std::string& class_name) {
    if (matrix.empty()) {
        LOG_ERROR_FUNC(class_name, "矩阵为空");
        return false;
    }
    
    if (expected_rows > 0 && matrix.rows != expected_rows) {
        LOG_ERROR_FUNC(class_name, "矩阵行数不匹配: 期望 " + std::to_string(expected_rows) + 
                      ", 实际 " + std::to_string(matrix.rows));
        return false;
    }
    
    if (expected_cols > 0 && matrix.cols != expected_cols) {
        LOG_ERROR_FUNC(class_name, "矩阵列数不匹配: 期望 " + std::to_string(expected_cols) + 
                      ", 实际 " + std::to_string(matrix.cols));
        return false;
    }
    
    return true;
}



cv::Mat OpenCVUtils::createMorphologyKernel(int kernel_size, int shape) {
    return cv::getStructuringElement(
        shape,
        cv::Size(kernel_size, kernel_size),
        cv::Point(-1, -1)
    );
}

double OpenCVUtils::calculateDistance(const cv::Point2f& p1, const cv::Point2f& p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    return std::sqrt(dx * dx + dy * dy);
}





} // namespace utils
} // namespace camera_calibration
