/**
 * @file main.cpp
 * @brief 相机标定系统主程序
 * <AUTHOR> Calibration Team
 * @date 2024
 */

#include "core/calibration_manager.h"
#include "core/config_manager.h"
#include "utils/file_utils.h"
#include <opencv2/opencv.hpp>
#include <iostream>
#include <string>
#include <chrono>
#include <fstream>

using namespace camera_calibration;
using namespace camera_calibration::core;

/**
 * @brief 打印程序使用说明
 */
void printUsage(const char* program_name) {
    std::cout << "相机标定系统 v1.0.0\n";
    std::cout << "用法: " << program_name << " [选项]\n\n";
    std::cout << "选项:\n";
    std::cout << "  -c, --config <path>      主配置文件路径 (默认: ../config/config.yaml)\n";
    std::cout << "  -i, --intrinsics <path>  相机内参文件路径 (默认: ../config/camera_intrinsics.yaml)\n";
    std::cout << "  -I, --input <path>       输入图像路径 (覆盖配置文件中的设置)\n";
    std::cout << "  -o, --output <path>      输出目录路径 (覆盖配置文件中的设置)\n";
    std::cout << "  -d, --debug              启用调试模式\n";
    std::cout << "  -v, --verbose            详细输出模式\n";
    std::cout << "  -h, --help               显示此帮助信息\n\n";
    std::cout << "示例:\n";
    std::cout << "  " << program_name << " --config config.yaml --input test.bmp --output ./results\n";
    std::cout << "  " << program_name << " --debug --verbose\n";
}

/**
 * @brief 解析命令行参数
 */
struct CommandLineArgs {
    std::string config_path = "../config/config_0717.yaml";
    std::string intrinsics_path = "../config/camera_intrinsics.yaml";
    std::string input_image_path;
    std::string output_path;
    std::string debug_path;
    bool debug_mode = false;
    bool verbose_mode = false;
    bool show_help = false;
};

CommandLineArgs parseCommandLine(int argc, char* argv[]) {
    CommandLineArgs args;
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            args.show_help = true;
        } else if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                args.config_path = argv[++i];
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数\n";
                args.show_help = true;
            }
        } else if (arg == "-i" || arg == "--intrinsics") {
            if (i + 1 < argc) {
                args.intrinsics_path = argv[++i];
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数\n";
                args.show_help = true;
            }
        } else if (arg == "-I" || arg == "--input") {
            if (i + 1 < argc) {
                args.input_image_path = argv[++i];
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数\n";
                args.show_help = true;
            }
        } else if (arg == "-o" || arg == "--output") {
            if (i + 1 < argc) {
                args.output_path = argv[++i];
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数\n";
                args.show_help = true;
            }
        } else if (arg == "-d" || arg == "--debug") {
            args.debug_mode = true;
        } else if (arg == "-v" || arg == "--verbose") {
            args.verbose_mode = true;
        } else {
            std::cerr << "未知选项: " << arg << "\n";
            args.show_help = true;
        }
    }
    
    return args;
}

/**
 * @brief 验证文件路径
 */
bool validateFilePath(const std::string& path, const std::string& description) {
    if (!utils::FileUtils::exists(path)) {
        std::cerr << "❌ " << description << " 不存在: " << path << std::endl;
        return false;
    }
    return true;
}

/**
 * @brief 主函数
 */
int main(int argc, char* argv[]) {
    auto start_time = std::chrono::high_resolution_clock::now();
        // 解析命令行参数
    CommandLineArgs args = parseCommandLine(argc, argv);
    if (args.show_help) {
        printUsage(argv[0]);
        return 0;
    }
    
    std::cout << "🚀 相机标定系统启动中...\n" << std::endl;
    try {
       
        // 验证配置文件存在
        if (!validateFilePath(args.config_path, "主配置文件") ||
            !validateFilePath(args.intrinsics_path, "相机内参文件")) {
            return -1;
        }
        // 加载配置
        std::cout << "📋 加载配置文件..." << std::endl;
        ConfigManager& config_mgr = ConfigManager::getInstance();
        if (!config_mgr.loadConfig(args.config_path, args.intrinsics_path)) {
            std::cerr << "❌ 配置文件加载失败" << std::endl;
            return -1;
        }
        std::cout << "✅ 配置加载成功" << std::endl;
        

        // 覆盖命令行参数
        std::string input_image_path = args.input_image_path.empty() ? 
                                      config_mgr.getInputImagePath() : args.input_image_path;
        std::string output_path = args.output_path.empty() ? 
                                 config_mgr.getSavePath() : args.output_path;
        
        std::string debug_path = args.debug_path.empty() ? 
                                 config_mgr.getDebugPath() : args.debug_path;
        
        // 验证输入图像
        if (!validateFilePath(input_image_path, "输入图像")) {
            return -1;
        }
        // 读取输入图像进行基本验证
        cv::Mat test_image = cv::imread(input_image_path, cv::IMREAD_COLOR);
        if (test_image.empty()) {
            std::cerr << "❌ 无法读取输入图像: " << input_image_path << std::endl;
            return -1;
        }
        
        std::cout << "📷 输入图像: " << input_image_path 
                  << " (尺寸: " << test_image.cols << "x" << test_image.rows << ")" << std::endl;
        std::cout << "📁 输出目录: " << output_path << std::endl;
       
        
        // 1. 初始化标定管理器
        std::cout << "\n🔧 初始化标定管理器..." << std::endl;
        CalibrationManager calib_mgr(config_mgr);
        if (args.debug_mode) {
            calib_mgr.setDebugMode(true);
            std::cout << "📁 调试目录: " << debug_path << std::endl;
            std::cout << "🐛 调试模式已启用" << std::endl;
        }
        
        if (!calib_mgr.initialize()) {
            std::cerr << "❌ 标定管理器初始化失败" << std::endl;
            return -1;
        }
        std::cout << "✅ 标定管理器初始化成功" << std::endl;
        std::cout <<"-------------------------------------------------------" <<std::endl;

        // 2. 执行标定流程
        std::cout << "\n🎯 开始执行标定流程..." << std::endl;
        ErrorCode result = calib_mgr.performCalibration(input_image_path, output_path, debug_path);
        std::cout <<"-------------------------------------------------------" <<std::endl;

        // 处理结果
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        if (result == ErrorCode::SUCCESS) {
            std::cout << "\n🎉 标定成功完成！" << std::endl;
            
            // 显示统计信息
            auto stats = calib_mgr.getLastCalibrationStats();
            std::cout << "\n📊 标定统计信息:" << std::endl;
            std::cout << "   验证状态: " << (stats.validation_passed ? "通过" : "失败") << std::endl;
            
            if (!stats.error_message.empty()) {
                std::cout << "   备注: " << stats.error_message << std::endl;
            }
            
            std::cout << "\n⏱️  总耗时: " << duration.count() << " 毫秒" << std::endl;
            std::cout << "📁 结果已保存到: " << output_path << std::endl;
            
            return 0;
        } else {
            std::cerr << "\n❌ 标定失败: " << errorCodeToString(result) << std::endl;
            std::cerr << "⏱️  耗时: " << duration.count() << " 毫秒" << std::endl;
            
            auto stats = calib_mgr.getLastCalibrationStats();
            if (!stats.error_message.empty()) {
                std::cerr << "错误详情: " << stats.error_message << std::endl;
            }
            
            return static_cast<int>(result);
        }
        
    } catch (const cv::Exception& e) {
        std::cerr << "❌ OpenCV 异常: " << e.what() << std::endl;
        return -1;
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "❌ 未知异常" << std::endl;
        return -1;
    }
}
