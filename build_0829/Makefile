# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/panpan/code/Calib/camera_calib-main_org

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/panpan/code/Calib/camera_calib-main_org/build_0829

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/Calib/camera_calib-main_org/build_0829/CMakeFiles /home/<USER>/panpan/code/Calib/camera_calib-main_org/build_0829//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/Calib/camera_calib-main_org/build_0829/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named camera_calibration_lib

# Build rule for target.
camera_calibration_lib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 camera_calibration_lib
.PHONY : camera_calibration_lib

# fast build rule for target.
camera_calibration_lib/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/build
.PHONY : camera_calibration_lib/fast

#=============================================================================
# Target rules for targets named camera_calibration

# Build rule for target.
camera_calibration: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 camera_calibration
.PHONY : camera_calibration

# fast build rule for target.
camera_calibration/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration.dir/build.make CMakeFiles/camera_calibration.dir/build
.PHONY : camera_calibration/fast

src/calibration/forward_calibrator.o: src/calibration/forward_calibrator.cpp.o
.PHONY : src/calibration/forward_calibrator.o

# target to build an object file
src/calibration/forward_calibrator.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/calibration/forward_calibrator.cpp.o
.PHONY : src/calibration/forward_calibrator.cpp.o

src/calibration/forward_calibrator.i: src/calibration/forward_calibrator.cpp.i
.PHONY : src/calibration/forward_calibrator.i

# target to preprocess a source file
src/calibration/forward_calibrator.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/calibration/forward_calibrator.cpp.i
.PHONY : src/calibration/forward_calibrator.cpp.i

src/calibration/forward_calibrator.s: src/calibration/forward_calibrator.cpp.s
.PHONY : src/calibration/forward_calibrator.s

# target to generate assembly for a file
src/calibration/forward_calibrator.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/calibration/forward_calibrator.cpp.s
.PHONY : src/calibration/forward_calibrator.cpp.s

src/core/calibration_manager.o: src/core/calibration_manager.cpp.o
.PHONY : src/core/calibration_manager.o

# target to build an object file
src/core/calibration_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/core/calibration_manager.cpp.o
.PHONY : src/core/calibration_manager.cpp.o

src/core/calibration_manager.i: src/core/calibration_manager.cpp.i
.PHONY : src/core/calibration_manager.i

# target to preprocess a source file
src/core/calibration_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/core/calibration_manager.cpp.i
.PHONY : src/core/calibration_manager.cpp.i

src/core/calibration_manager.s: src/core/calibration_manager.cpp.s
.PHONY : src/core/calibration_manager.s

# target to generate assembly for a file
src/core/calibration_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/core/calibration_manager.cpp.s
.PHONY : src/core/calibration_manager.cpp.s

src/core/config_manager.o: src/core/config_manager.cpp.o
.PHONY : src/core/config_manager.o

# target to build an object file
src/core/config_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/core/config_manager.cpp.o
.PHONY : src/core/config_manager.cpp.o

src/core/config_manager.i: src/core/config_manager.cpp.i
.PHONY : src/core/config_manager.i

# target to preprocess a source file
src/core/config_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/core/config_manager.cpp.i
.PHONY : src/core/config_manager.cpp.i

src/core/config_manager.s: src/core/config_manager.cpp.s
.PHONY : src/core/config_manager.s

# target to generate assembly for a file
src/core/config_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/core/config_manager.cpp.s
.PHONY : src/core/config_manager.cpp.s

src/core/types.o: src/core/types.cpp.o
.PHONY : src/core/types.o

# target to build an object file
src/core/types.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/core/types.cpp.o
.PHONY : src/core/types.cpp.o

src/core/types.i: src/core/types.cpp.i
.PHONY : src/core/types.i

# target to preprocess a source file
src/core/types.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/core/types.cpp.i
.PHONY : src/core/types.cpp.i

src/core/types.s: src/core/types.cpp.s
.PHONY : src/core/types.s

# target to generate assembly for a file
src/core/types.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/core/types.cpp.s
.PHONY : src/core/types.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration.dir/build.make CMakeFiles/camera_calibration.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration.dir/build.make CMakeFiles/camera_calibration.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration.dir/build.make CMakeFiles/camera_calibration.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/processing/coordinate_mapper.o: src/processing/coordinate_mapper.cpp.o
.PHONY : src/processing/coordinate_mapper.o

# target to build an object file
src/processing/coordinate_mapper.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/coordinate_mapper.cpp.o
.PHONY : src/processing/coordinate_mapper.cpp.o

src/processing/coordinate_mapper.i: src/processing/coordinate_mapper.cpp.i
.PHONY : src/processing/coordinate_mapper.i

# target to preprocess a source file
src/processing/coordinate_mapper.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/coordinate_mapper.cpp.i
.PHONY : src/processing/coordinate_mapper.cpp.i

src/processing/coordinate_mapper.s: src/processing/coordinate_mapper.cpp.s
.PHONY : src/processing/coordinate_mapper.s

# target to generate assembly for a file
src/processing/coordinate_mapper.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/coordinate_mapper.cpp.s
.PHONY : src/processing/coordinate_mapper.cpp.s

src/processing/feature_detector.o: src/processing/feature_detector.cpp.o
.PHONY : src/processing/feature_detector.o

# target to build an object file
src/processing/feature_detector.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/feature_detector.cpp.o
.PHONY : src/processing/feature_detector.cpp.o

src/processing/feature_detector.i: src/processing/feature_detector.cpp.i
.PHONY : src/processing/feature_detector.i

# target to preprocess a source file
src/processing/feature_detector.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/feature_detector.cpp.i
.PHONY : src/processing/feature_detector.cpp.i

src/processing/feature_detector.s: src/processing/feature_detector.cpp.s
.PHONY : src/processing/feature_detector.s

# target to generate assembly for a file
src/processing/feature_detector.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/feature_detector.cpp.s
.PHONY : src/processing/feature_detector.cpp.s

src/processing/image_enhancer.o: src/processing/image_enhancer.cpp.o
.PHONY : src/processing/image_enhancer.o

# target to build an object file
src/processing/image_enhancer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/image_enhancer.cpp.o
.PHONY : src/processing/image_enhancer.cpp.o

src/processing/image_enhancer.i: src/processing/image_enhancer.cpp.i
.PHONY : src/processing/image_enhancer.i

# target to preprocess a source file
src/processing/image_enhancer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/image_enhancer.cpp.i
.PHONY : src/processing/image_enhancer.cpp.i

src/processing/image_enhancer.s: src/processing/image_enhancer.cpp.s
.PHONY : src/processing/image_enhancer.s

# target to generate assembly for a file
src/processing/image_enhancer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/image_enhancer.cpp.s
.PHONY : src/processing/image_enhancer.cpp.s

src/processing/msrcr.o: src/processing/msrcr.cpp.o
.PHONY : src/processing/msrcr.o

# target to build an object file
src/processing/msrcr.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/msrcr.cpp.o
.PHONY : src/processing/msrcr.cpp.o

src/processing/msrcr.i: src/processing/msrcr.cpp.i
.PHONY : src/processing/msrcr.i

# target to preprocess a source file
src/processing/msrcr.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/msrcr.cpp.i
.PHONY : src/processing/msrcr.cpp.i

src/processing/msrcr.s: src/processing/msrcr.cpp.s
.PHONY : src/processing/msrcr.s

# target to generate assembly for a file
src/processing/msrcr.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/processing/msrcr.cpp.s
.PHONY : src/processing/msrcr.cpp.s

src/utils/common_utils.o: src/utils/common_utils.cpp.o
.PHONY : src/utils/common_utils.o

# target to build an object file
src/utils/common_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/common_utils.cpp.o
.PHONY : src/utils/common_utils.cpp.o

src/utils/common_utils.i: src/utils/common_utils.cpp.i
.PHONY : src/utils/common_utils.i

# target to preprocess a source file
src/utils/common_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/common_utils.cpp.i
.PHONY : src/utils/common_utils.cpp.i

src/utils/common_utils.s: src/utils/common_utils.cpp.s
.PHONY : src/utils/common_utils.s

# target to generate assembly for a file
src/utils/common_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/common_utils.cpp.s
.PHONY : src/utils/common_utils.cpp.s

src/utils/file_utils.o: src/utils/file_utils.cpp.o
.PHONY : src/utils/file_utils.o

# target to build an object file
src/utils/file_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/file_utils.cpp.o
.PHONY : src/utils/file_utils.cpp.o

src/utils/file_utils.i: src/utils/file_utils.cpp.i
.PHONY : src/utils/file_utils.i

# target to preprocess a source file
src/utils/file_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/file_utils.cpp.i
.PHONY : src/utils/file_utils.cpp.i

src/utils/file_utils.s: src/utils/file_utils.cpp.s
.PHONY : src/utils/file_utils.s

# target to generate assembly for a file
src/utils/file_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/file_utils.cpp.s
.PHONY : src/utils/file_utils.cpp.s

src/utils/map_computer.o: src/utils/map_computer.cpp.o
.PHONY : src/utils/map_computer.o

# target to build an object file
src/utils/map_computer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/map_computer.cpp.o
.PHONY : src/utils/map_computer.cpp.o

src/utils/map_computer.i: src/utils/map_computer.cpp.i
.PHONY : src/utils/map_computer.i

# target to preprocess a source file
src/utils/map_computer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/map_computer.cpp.i
.PHONY : src/utils/map_computer.cpp.i

src/utils/map_computer.s: src/utils/map_computer.cpp.s
.PHONY : src/utils/map_computer.s

# target to generate assembly for a file
src/utils/map_computer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/map_computer.cpp.s
.PHONY : src/utils/map_computer.cpp.s

src/utils/opencv_utils.o: src/utils/opencv_utils.cpp.o
.PHONY : src/utils/opencv_utils.o

# target to build an object file
src/utils/opencv_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/opencv_utils.cpp.o
.PHONY : src/utils/opencv_utils.cpp.o

src/utils/opencv_utils.i: src/utils/opencv_utils.cpp.i
.PHONY : src/utils/opencv_utils.i

# target to preprocess a source file
src/utils/opencv_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/opencv_utils.cpp.i
.PHONY : src/utils/opencv_utils.cpp.i

src/utils/opencv_utils.s: src/utils/opencv_utils.cpp.s
.PHONY : src/utils/opencv_utils.s

# target to generate assembly for a file
src/utils/opencv_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/camera_calibration_lib.dir/build.make CMakeFiles/camera_calibration_lib.dir/src/utils/opencv_utils.cpp.s
.PHONY : src/utils/opencv_utils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... camera_calibration"
	@echo "... camera_calibration_lib"
	@echo "... src/calibration/forward_calibrator.o"
	@echo "... src/calibration/forward_calibrator.i"
	@echo "... src/calibration/forward_calibrator.s"
	@echo "... src/core/calibration_manager.o"
	@echo "... src/core/calibration_manager.i"
	@echo "... src/core/calibration_manager.s"
	@echo "... src/core/config_manager.o"
	@echo "... src/core/config_manager.i"
	@echo "... src/core/config_manager.s"
	@echo "... src/core/types.o"
	@echo "... src/core/types.i"
	@echo "... src/core/types.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/processing/coordinate_mapper.o"
	@echo "... src/processing/coordinate_mapper.i"
	@echo "... src/processing/coordinate_mapper.s"
	@echo "... src/processing/feature_detector.o"
	@echo "... src/processing/feature_detector.i"
	@echo "... src/processing/feature_detector.s"
	@echo "... src/processing/image_enhancer.o"
	@echo "... src/processing/image_enhancer.i"
	@echo "... src/processing/image_enhancer.s"
	@echo "... src/processing/msrcr.o"
	@echo "... src/processing/msrcr.i"
	@echo "... src/processing/msrcr.s"
	@echo "... src/utils/common_utils.o"
	@echo "... src/utils/common_utils.i"
	@echo "... src/utils/common_utils.s"
	@echo "... src/utils/file_utils.o"
	@echo "... src/utils/file_utils.i"
	@echo "... src/utils/file_utils.s"
	@echo "... src/utils/map_computer.o"
	@echo "... src/utils/map_computer.i"
	@echo "... src/utils/map_computer.s"
	@echo "... src/utils/opencv_utils.o"
	@echo "... src/utils/opencv_utils.i"
	@echo "... src/utils/opencv_utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

